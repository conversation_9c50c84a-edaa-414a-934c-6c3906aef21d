#!/bin/bash
# Development environment runner

# Initialize pyenv if available
if command -v pyenv 1>/dev/null 2>&1; then
  eval "$(pyenv init -)"
fi

# Use python3 if python is not available
PYTHON_CMD="python"
if ! command -v python &> /dev/null; then
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    else
        echo "❌ Error: Neither python nor python3 found!"
        exit 1
    fi
fi

echo "🔄 Switching to development environment..."
$PYTHON_CMD scripts/switch_env.py dev
echo "🚀 Starting development server..."
$PYTHON_CMD -m uvicorn api.main:app --reload --host 127.0.0.1 --port 8000
